# osmAG XML Semantic版本兼容性测试工具

## 概述

`test_compatibility_semantic.py` 是专门用于验证semantic版本osmAG XML文件的兼容性测试工具。该工具基于 `semantic2fix_id.py` 的转换逻辑，深入理解semantic版本与fix_id版本之间的差异，提供针对性的验证功能。

## Semantic版本 vs Fix_ID版本差异

### 1. ID格式差异
- **Semantic版本**：使用语义化名称（如 `E1a-F2-01`, `E2-P3`, `E1-ST-01`）
- **Fix_ID版本**：使用数字ID（如 `-125806`, `-164394`）

### 2. 引用方式差异
- **Semantic版本**：通过 `name` 标签进行引用
- **Fix_ID版本**：通过数字ID进行引用

### 3. 跨楼层通道处理
- **Semantic版本**：`osmAG:from` 和 `osmAG:to` 可以相同（表示跨楼层连接）
- **Fix_ID版本**：转换后分别指向不同楼层的具体ID

### 4. 垂直交通设施
- **Semantic版本**：同名电梯/楼梯在不同楼层使用相同名称
- **Fix_ID版本**：每个楼层的设施有独立的数字ID

## 功能特性

### 🔍 Semantic版本特有验证
- **语义化命名规则验证**：检查是否符合 `E1a-F2-01` 等格式
- **跨楼层通道验证**：验证 `from==to` 的特殊逻辑
- **垂直交通设施验证**：检查电梯/楼梯的多楼层一致性
- **语义引用完整性**：验证所有引用的区域是否存在

### 📊 详细统计分析
- **垂直交通设施分布**：按楼层统计电梯和楼梯
- **跨楼层通道分析**：按类型（电梯/楼梯）统计
- **语义命名模式**：统计各种命名模式的使用情况
- **引用关系图谱**：基于语义化名称的连接分析

### 🚀 批量处理能力
- **单文件验证**：详细分析单个semantic版本文件
- **批量验证**：一次验证多个文件或整个目录
- **递归搜索**：自动查找目录中的所有.osm文件

## 安装要求

```bash
# Python 3.6+ 
# 标准库依赖（无需额外安装）：
# - xml.etree.ElementTree
# - argparse
# - json
# - pathlib
# - dataclasses
# - enum
# - re
```

## 使用方法

### 基本用法

```bash
# 验证单个semantic版本文件
python test_compatibility_semantic.py semantic_file.osm

# 验证semantic_tags目录中的所有文件
python test_compatibility_semantic.py area_graph_data_parser/data/semantic_tags/

# 详细模式验证
python test_compatibility_semantic.py semantic_file.osm --verbose

# 保存验证报告
python test_compatibility_semantic.py semantic_file.osm --save-report

# 批量验证多个文件
python test_compatibility_semantic.py file1.osm file2.osm file3.osm
```

### 命令行参数

- `paths`: 要验证的semantic版本osmAG文件路径或目录路径（必需）
- `-v, --verbose`: 显示详细的验证信息
- `-s, --save-report`: 保存验证报告到JSON文件
- `--version`: 显示版本信息
- `-h, --help`: 显示帮助信息

## Semantic版本验证规则

### 语义化命名模式
- **区域名称**：`E1a-F2-01` (楼栋-楼层-房间)
- **结构名称**：`E1a-F2` (楼栋-楼层)
- **电梯名称**：`E2-P3` (楼栋-电梯编号)
- **楼梯名称**：`E1-ST-01` (楼栋-楼梯编号)
- **通道名称**：`E1a-F2-01_to_E1a-F2-COR-01` (起始_to_目标)

### 跨楼层通道规则
- `osmAG:from` 和 `osmAG:to` 必须相同
- 引用的必须是垂直交通设施（电梯或楼梯）
- 必须有 `level` 标签指示通道所在楼层
- 相邻楼层必须存在对应的垂直交通设施

### 垂直交通设施规则
- 同名设施应在多个楼层存在
- 每个楼层的同名设施不能重复定义
- 命名必须符合电梯/楼梯的格式要求

## 输出示例

```
================================================================================
osmAG XML Semantic版本兼容性验证报告
================================================================================

📊 统计信息:
  节点总数: 2135
  路径总数: 221
  区域总数: 98
  通道总数: 113
  垂直交通设施: 0
  跨楼层通道: 0
  各层级区域分布:
    层级 2: 97 个区域
  语义化命名模式分布:
    structure: 6 个
    passage: 113 个
    area: 92 个
  平均每区域连接数: 2.06

🔍 验证结果:
  错误: 31 个
  警告: 3 个
  信息: 0 个

📋 详细问题列表:

  ERROR:
    SEMANTIC_REFERENCE (28 个):
      - 通道引用了不存在的目标区域: E1-ST-08 [元素: -189685]
      - 通道引用了不存在的起始区域: E2-P4 [元素: -189697]
      ...

🎯 验证结论:
  ❌ 文件存在兼容性问题，需要修复
================================================================================
```

## 常见问题解决

### 1. 语义引用错误
**错误**：`通道引用了不存在的起始区域: E1-ST-01`
**解决**：确保引用的区域在文件中存在，检查名称拼写

### 2. 命名格式不标准
**警告**：`区域名称格式不标准: InvalidName`
**解决**：按照语义化命名规则修改名称

### 3. 跨楼层通道配置错误
**错误**：`跨楼层通道的from和to应该相同`
**解决**：确保跨楼层通道的from和to引用相同的垂直交通设施

### 4. 垂直交通设施缺失
**警告**：`垂直交通设施'E2-P3'只在一个楼层存在`
**解决**：在相关楼层添加对应的电梯/楼梯定义

## 与Fix_ID版本工具的区别

| 特性 | Semantic版本工具 | Fix_ID版本工具 |
|------|------------------|----------------|
| ID验证 | 语义化名称格式 | 数字ID格式 |
| 引用检查 | 基于name标签 | 基于数字ID |
| 跨楼层通道 | 特殊from==to逻辑 | 标准from!=to |
| 垂直交通 | 多楼层同名验证 | 单独ID验证 |
| 命名模式 | 语义化模式统计 | 不适用 |

## 技术实现

该工具完全基于semantic2fix_id.py的转换逻辑实现：
- 理解semantic版本的特殊标签格式和命名规则
- 实现跨楼层通道的特殊验证逻辑
- 提供比转换脚本更详细的错误报告和统计信息
- 确保semantic版本文件能够正确转换为fix_id版本

## 贡献

如需改进或扩展功能，请参考semantic2fix_id.py中的最新转换逻辑，确保验证规则与转换要求保持同步。
