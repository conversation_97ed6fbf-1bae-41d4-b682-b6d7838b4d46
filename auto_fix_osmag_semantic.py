#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
osmAG Semantic版本自动修复脚本

该脚本用于自动修复semantic版本osmAG XML文件中的常见问题。
基于test_compatibility_semantic.py检测出的问题类型，提供针对性的自动修复功能。

支持的修复类型：
1. 根元素标准化
2. 属性值标准化
3. 语义化命名格式修复
4. 跨楼层通道配置修复
5. 缺失标签补充
6. 层级一致性修复

作者: AI Assistant
日期: 2024
"""

import xml.etree.ElementTree as ET
import argparse
import sys
import os
import shutil
import re
from typing import Dict, List, Set, Tuple, Optional, Any
from dataclasses import dataclass, field
from enum import Enum
import json
from pathlib import Path
from datetime import datetime

# 导入验证工具
try:
    from test_compatibility_semantic import SemanticOSMAGValidator, ValidationLevel
except ImportError:
    print("错误: 无法导入test_compatibility_semantic模块，请确保文件在同一目录下")
    sys.exit(1)


class FixLevel(Enum):
    """修复级别枚举"""
    SAFE = "SAFE"           # 安全修复，不会改变语义
    MODERATE = "MODERATE"   # 中等修复，可能轻微改变语义
    RISKY = "RISKY"        # 风险修复，可能显著改变语义


@dataclass
class FixResult:
    """修复结果数据类"""
    level: FixLevel
    category: str
    description: str
    element_id: Optional[str] = None
    old_value: Optional[str] = None
    new_value: Optional[str] = None
    success: bool = True


class OSMAGSemanticFixer:
    """Semantic版本osmAG文件自动修复器"""
    
    def __init__(self, enable_moderate_fixes: bool = False, enable_risky_fixes: bool = False):
        self.enable_moderate_fixes = enable_moderate_fixes
        self.enable_risky_fixes = enable_risky_fixes
        self.fix_results: List[FixResult] = []
        self.stats = {
            'files_processed': 0,
            'files_fixed': 0,
            'total_fixes': 0,
            'safe_fixes': 0,
            'moderate_fixes': 0,
            'risky_fixes': 0,
            'failed_fixes': 0
        }
        
        # Semantic版本命名规则模式
        self.semantic_patterns = {
            'area_name': re.compile(r'^[A-Z]\d+[a-z]?-F\d+-.*$'),  # E1a-F2-01
            'structure_name': re.compile(r'^[A-Z]\d+[a-z]?-F\d+$'),  # E1a-F2
            'elevator_name': re.compile(r'^[A-Z]\d+-P\d+$'),  # E2-P3
            'stair_name': re.compile(r'^[A-Z]\d+-ST-\d+$'),  # E1-ST-01
            'passage_name': re.compile(r'^.*_to_.*$'),  # E1a-F2-01_to_E1a-F2-COR-01
        }
    
    def add_fix_result(self, level: FixLevel, category: str, description: str,
                      element_id: Optional[str] = None, old_value: Optional[str] = None,
                      new_value: Optional[str] = None, success: bool = True):
        """添加修复结果"""
        result = FixResult(
            level=level,
            category=category,
            description=description,
            element_id=element_id,
            old_value=old_value,
            new_value=new_value,
            success=success
        )
        self.fix_results.append(result)
        
        # 更新统计
        self.stats['total_fixes'] += 1
        if success:
            if level == FixLevel.SAFE:
                self.stats['safe_fixes'] += 1
            elif level == FixLevel.MODERATE:
                self.stats['moderate_fixes'] += 1
            else:
                self.stats['risky_fixes'] += 1
        else:
            self.stats['failed_fixes'] += 1
    
    def fix_file(self, file_path: str, output_path: Optional[str] = None, 
                 create_backup: bool = True) -> bool:
        """修复单个文件"""
        try:
            self.fix_results.clear()
            
            # 检查文件存在性
            if not os.path.exists(file_path):
                print(f"❌ 文件不存在: {file_path}")
                return False
            
            # 创建备份
            if create_backup:
                backup_path = f"{file_path}.backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
                shutil.copy2(file_path, backup_path)
                print(f"📁 已创建备份: {backup_path}")
            
            # 解析XML
            try:
                tree = ET.parse(file_path)
                root = tree.getroot()
            except ET.ParseError as e:
                print(f"❌ XML解析错误: {e}")
                return False
            
            # 执行修复
            fixed = False
            
            # 1. 修复根元素
            if self._fix_root_element(root):
                fixed = True
            
            # 2. 修复节点
            if self._fix_nodes(root):
                fixed = True
            
            # 3. 修复路径
            if self._fix_ways(root):
                fixed = True
            
            # 4. 修复区域
            if self._fix_areas(root):
                fixed = True
            
            # 5. 修复通道
            if self._fix_passages(root):
                fixed = True
            
            # 6. 修复跨楼层通道
            if self._fix_cross_level_passages(root):
                fixed = True
            
            # 7. 修复层级一致性
            if self.enable_moderate_fixes:
                if self._fix_level_consistency(root):
                    fixed = True
            
            # 保存修复后的文件
            if fixed:
                output_file = output_path if output_path else file_path
                
                # 格式化XML输出
                self._format_xml(root)
                tree.write(output_file, encoding='utf-8', xml_declaration=True)
                
                print(f"✅ 文件已修复: {output_file}")
                self.stats['files_fixed'] += 1
            else:
                print(f"ℹ️  文件无需修复: {file_path}")
            
            self.stats['files_processed'] += 1
            return True
            
        except Exception as e:
            print(f"❌ 修复过程中发生异常: {e}")
            return False
    
    def _fix_root_element(self, root: ET.Element) -> bool:
        """修复根元素"""
        fixed = False
        
        # 修复版本号
        if root.attrib.get("version") != "0.6":
            old_version = root.attrib.get("version", "未设置")
            root.attrib["version"] = "0.6"
            self.add_fix_result(FixLevel.SAFE, "ROOT", "标准化版本号",
                              old_value=old_version, new_value="0.6")
            fixed = True
        
        # 添加generator属性
        if "generator" not in root.attrib:
            root.attrib["generator"] = "osmAG_semantic_auto_fixer"
            self.add_fix_result(FixLevel.SAFE, "ROOT", "添加generator属性",
                              new_value="osmAG_semantic_auto_fixer")
            fixed = True
        
        return fixed
    
    def _fix_nodes(self, root: ET.Element) -> bool:
        """修复节点"""
        fixed = False
        
        for node_elem in root.findall("node"):
            node_id = node_elem.attrib.get("id", "unknown")
            
            # 修复action属性
            if "action" not in node_elem.attrib:
                node_elem.attrib["action"] = "modify"
                self.add_fix_result(FixLevel.SAFE, "NODE", "添加action属性",
                                  element_id=node_id, new_value="modify")
                fixed = True
            elif node_elem.attrib["action"] not in ["modify", "delete", "create"]:
                old_action = node_elem.attrib["action"]
                node_elem.attrib["action"] = "modify"
                self.add_fix_result(FixLevel.SAFE, "NODE", "标准化action属性",
                                  element_id=node_id, old_value=old_action, new_value="modify")
                fixed = True
            
            # 修复visible属性
            if "visible" not in node_elem.attrib:
                node_elem.attrib["visible"] = "true"
                self.add_fix_result(FixLevel.SAFE, "NODE", "添加visible属性",
                                  element_id=node_id, new_value="true")
                fixed = True
            
            # 修复坐标格式（如果启用中等修复）
            if self.enable_moderate_fixes:
                if self._fix_coordinate_format(node_elem, node_id):
                    fixed = True
        
        return fixed

    def _fix_coordinate_format(self, node_elem: ET.Element, node_id: str) -> bool:
        """修复坐标格式"""
        fixed = False

        # 修复纬度
        lat_str = node_elem.attrib.get("lat", "")
        if lat_str:
            try:
                lat = float(lat_str)
                # 检查范围
                if lat < -90 or lat > 90:
                    # 尝试简单的修正
                    if lat < -90:
                        new_lat = -90.0
                    else:
                        new_lat = 90.0

                    node_elem.attrib["lat"] = str(new_lat)
                    self.add_fix_result(FixLevel.MODERATE, "NODE", "修正纬度范围",
                                      element_id=node_id, old_value=lat_str, new_value=str(new_lat))
                    fixed = True
            except ValueError:
                pass  # 无法修复的格式错误

        # 修复经度
        lon_str = node_elem.attrib.get("lon", "")
        if lon_str:
            try:
                lon = float(lon_str)
                # 检查范围
                if lon < -180 or lon > 180:
                    # 尝试简单的修正
                    if lon < -180:
                        new_lon = -180.0
                    else:
                        new_lon = 180.0

                    node_elem.attrib["lon"] = str(new_lon)
                    self.add_fix_result(FixLevel.MODERATE, "NODE", "修正经度范围",
                                      element_id=node_id, old_value=lon_str, new_value=str(new_lon))
                    fixed = True
            except ValueError:
                pass  # 无法修复的格式错误

        return fixed

    def _fix_ways(self, root: ET.Element) -> bool:
        """修复路径"""
        fixed = False

        for way_elem in root.findall("way"):
            way_id = way_elem.attrib.get("id", "unknown")

            # 修复action属性
            if "action" not in way_elem.attrib:
                way_elem.attrib["action"] = "modify"
                self.add_fix_result(FixLevel.SAFE, "WAY", "添加action属性",
                                  element_id=way_id, new_value="modify")
                fixed = True

            # 修复visible属性
            if "visible" not in way_elem.attrib:
                way_elem.attrib["visible"] = "true"
                self.add_fix_result(FixLevel.SAFE, "WAY", "添加visible属性",
                                  element_id=way_id, new_value="true")
                fixed = True

        return fixed

    def _fix_areas(self, root: ET.Element) -> bool:
        """修复区域"""
        fixed = False

        for way_elem in root.findall("way"):
            way_id = way_elem.attrib.get("id", "unknown")

            # 检查是否是区域
            tags = {}
            for tag_elem in way_elem.findall("tag"):
                k = tag_elem.attrib.get("k", "")
                v = tag_elem.attrib.get("v", "")
                tags[k] = v

            if tags.get("osmAG:type") == "area":
                # 修复区域闭合性
                if self._fix_area_closure(way_elem, way_id):
                    fixed = True

                # 修复缺失的必需标签
                if self._fix_missing_area_tags(way_elem, way_id, tags):
                    fixed = True

                # 标准化区域类型
                if self._fix_area_type(way_elem, way_id, tags):
                    fixed = True

                # 修复语义化命名（如果启用中等修复）
                if self.enable_moderate_fixes:
                    if self._fix_semantic_naming(way_elem, way_id, tags):
                        fixed = True

                # 修复层级格式
                if self.enable_moderate_fixes:
                    if self._fix_level_format(way_elem, way_id, tags):
                        fixed = True

        return fixed

    def _fix_area_closure(self, way_elem: ET.Element, way_id: str) -> bool:
        """修复区域闭合性"""
        node_refs = []
        for nd_elem in way_elem.findall("nd"):
            ref = nd_elem.attrib.get("ref", "")
            if ref:
                node_refs.append(ref)

        if len(node_refs) >= 3 and node_refs[0] != node_refs[-1]:
            # 添加闭合节点
            new_nd = ET.SubElement(way_elem, "nd")
            new_nd.attrib["ref"] = node_refs[0]

            self.add_fix_result(FixLevel.SAFE, "AREA", "修复区域闭合性",
                              element_id=way_id,
                              old_value=f"首节点{node_refs[0]} != 尾节点{node_refs[-1]}",
                              new_value=f"已闭合到节点{node_refs[0]}")
            return True

        return False

    def _fix_missing_area_tags(self, way_elem: ET.Element, way_id: str, tags: Dict[str, str]) -> bool:
        """修复缺失的区域标签"""
        fixed = False

        # 检查osmAG:areaType
        if "osmAG:areaType" not in tags:
            # 尝试智能推断
            area_type = self._infer_area_type(tags)
            if area_type:
                new_tag = ET.SubElement(way_elem, "tag")
                new_tag.attrib["k"] = "osmAG:areaType"
                new_tag.attrib["v"] = area_type

                self.add_fix_result(FixLevel.MODERATE, "AREA", "智能推断区域类型",
                                  element_id=way_id, new_value=area_type)
                fixed = True

        # 检查level标签
        if "level" not in tags:
            # 尝试从name中推断层级
            name = tags.get("name", "")
            level = self._infer_level_from_name(name)
            if level:
                new_tag = ET.SubElement(way_elem, "tag")
                new_tag.attrib["k"] = "level"
                new_tag.attrib["v"] = level

                self.add_fix_result(FixLevel.MODERATE, "AREA", "从名称推断层级",
                                  element_id=way_id, new_value=level)
                fixed = True

        return fixed

    def _infer_area_type(self, tags: Dict[str, str]) -> Optional[str]:
        """智能推断区域类型"""
        name = tags.get("name", "").lower()

        if "corridor" in name or "cor" in name:
            return "corridor"
        elif "room" in name or any(x in name for x in ["office", "lab", "classroom"]):
            return "room"
        elif "elevator" in name or "lift" in name or re.search(r'p\d+', name):
            return "elevator"
        elif "stair" in name or "st-" in name:
            return "stairs"
        elif "structure" in name or "building" in name:
            return "structure"
        else:
            return "room"  # 默认为房间

    def _infer_level_from_name(self, name: str) -> Optional[str]:
        """从名称推断层级"""
        # 匹配 E1a-F2-01 格式中的 F2 部分
        match = re.search(r'-F(\d+)-', name)
        if match:
            return match.group(1)

        # 匹配其他可能的层级格式
        if "f1" in name.lower() or "1f" in name.lower():
            return "1"
        elif "f2" in name.lower() or "2f" in name.lower():
            return "2"
        elif "f3" in name.lower() or "3f" in name.lower():
            return "3"

        return None
