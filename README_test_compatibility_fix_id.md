# osmAG XML兼容性测试工具

## 概述

`test_compatibility.py` 是一个独立的Python测试脚本，用于验证新生成的osmAG XML文件是否完全兼容现有的C++解析器标准和格式要求。该工具基于 `area_graph_data_parser` 包中的C++解析逻辑实现，提供全面的兼容性验证功能。

## 功能特性

### 🔍 全面验证
- **XML结构验证**：检查根元素、属性格式、标签结构
- **数据类型验证**：验证节点坐标、ID格式、数值范围
- **必需字段检查**：确保所有必需的osmAG标签存在
- **数据完整性验证**：检查节点引用、区域闭合性、通道连接性
- **交叉验证**：验证通道与区域的关联关系、层级一致性

### 📊 详细报告
- **统计信息**：节点、区域、通道数量统计，各层级分布
- **问题分类**：按错误级别（ERROR/WARNING/INFO）和类别分组
- **元素定位**：提供具体的元素ID和行号信息
- **JSON报告**：可保存详细的验证报告供后续分析

### 🚀 批量处理
- **单文件验证**：验证单个osmAG文件
- **批量验证**：一次验证多个文件或整个目录
- **递归搜索**：自动查找目录中的所有.osm文件

## 安装要求

```bash
# Python 3.6+ 
# 标准库依赖（无需额外安装）：
# - xml.etree.ElementTree
# - argparse
# - json
# - pathlib
# - dataclasses
# - enum
```

## 使用方法

### 基本用法

```bash
# 验证单个文件
python test_compatibility.py file.osm

# 验证目录中的所有文件
python test_compatibility.py /path/to/directory/

# 详细模式验证
python test_compatibility.py file.osm --verbose

# 保存验证报告
python test_compatibility.py file.osm --save-report

# 批量验证多个文件
python test_compatibility.py file1.osm file2.osm file3.osm
```

### 命令行参数

- `paths`: 要验证的osmAG文件路径或目录路径（必需）
- `-v, --verbose`: 显示详细的验证信息
- `-s, --save-report`: 保存验证报告到JSON文件
- `--version`: 显示版本信息
- `-h, --help`: 显示帮助信息

## 验证标准

### XML结构要求
- 根元素必须是 `<osm version="0.6">`
- 节点元素：`<node id="..." lat="..." lon="..." action="modify" visible="true">`
- 路径元素：`<way id="..." action="modify" visible="true">`
- 标签元素：`<tag k="..." v="...">`

### 数据验证规则

#### 节点验证
- **必需属性**：id, lat, lon, action, visible
- **坐标范围**：纬度 [-90, 90]，经度 [-180, 180]
- **action值**：modify, delete, create

#### 区域验证
- **必需标签**：osmAG:type="area", osmAG:areaType, level
- **区域类型**：room, corridor, structure, elevator, stair
- **闭合性**：首尾节点必须相同
- **节点数量**：至少3个节点
- **父子关系**：osmAG:parent引用的区域必须存在

#### 通道验证
- **必需标签**：osmAG:type="passage", osmAG:from, osmAG:to
- **节点数量**：恰好2个节点
- **区域引用**：from/to引用的区域必须存在
- **连接性**：from和to不能相同

### 交叉验证
- 通道节点应在连接区域的边界上
- 父子区域的层级应保持一致
- 节点引用的完整性检查

## 输出示例

```
================================================================================
osmAG XML兼容性验证报告
================================================================================

📊 统计信息:
  节点总数: 408
  路径总数: 59
  区域总数: 27
  通道总数: 32
  各层级区域分布:
    层级 2: 27 个区域
  平均每区域连接数: 2.00

🔍 验证结果:
  错误: 0 个
  警告: 2 个
  信息: 0 个

📋 详细问题列表:

  WARNING:
    CROSS_VALIDATION (2 个):
      - 通道节点-164394不在连接的区域边界上 [元素: -125806]
      - 通道节点-164395不在连接的区域边界上 [元素: -125806]

🎯 验证结论:
  ✅ 文件通过兼容性验证
================================================================================
```

## 验证级别说明

- **ERROR** 🔴：严重错误，会导致C++解析器失败
- **WARNING** 🟡：警告，可能影响功能但不会导致解析失败
- **INFO** 🔵：信息提示，建议改进的地方

## 常见问题解决

### 1. 区域未闭合
**错误**：`区域未闭合: 首节点xxx != 尾节点yyy`
**解决**：确保区域的第一个和最后一个节点ID相同

### 2. 引用不存在的节点/区域
**错误**：`引用了不存在的节点/区域: xxx`
**解决**：检查节点/区域ID是否正确，确保被引用的元素存在

### 3. 缺少必需标签
**错误**：`区域/通道缺少必需标签: xxx`
**解决**：添加缺失的osmAG标签

### 4. 通道节点不在区域边界上
**警告**：`通道节点xxx不在连接的区域边界上`
**解决**：确保通道的节点在连接的两个区域的边界上

## 技术实现

该工具完全基于area_graph_data_parser包中的C++解析逻辑实现：
- 复现了`Parsing_Osm2AreaGraph`函数的验证逻辑
- 实现了与C++解析器相同的数据结构和验证规则
- 提供了比C++解析器更详细的错误报告和统计信息

## 贡献

如需改进或扩展功能，请参考area_graph_data_parser包中的最新C++实现，确保验证逻辑保持同步。
